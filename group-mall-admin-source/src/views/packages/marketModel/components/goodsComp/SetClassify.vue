<!--
 * @description: 抽离开源版本
 * @Author: chuyinlong
 * @Date: 2021-08-20 17:29:10
 * @LastEditors: chuyinlong
 * @LastEditTime: 2021-08-25 13:47:50
-->
<template>
  <!-- 设置分类 -->
  <div class="setClassify">
    <div @click="handleClick" class="setClassify__title"
      v-if="isSupper || (editButton && isItem) || (setClassificationButton && !isItem)">
      <el-popover v-model="popVisible" placement="bottom" width="260" trigger="hover"
        v-if="!isItem && (isSupper || setClassificationButton)" @show="showGetId">
        <GoodCategory :cate-type="'2'" :pop-visible="popVisible" @getNewCateList="getNewCateList"
          @chPopVisible="popVisible = false" :showGetList="showGetList" ref="goodCate" style="width: 260px">
        </GoodCategory>
        <span slot="reference">
          <slot>设置分类</slot>
        </span>
      </el-popover>
      <el-popover v-model="popVisible" placement="bottom" width="260" trigger="click"
        v-if="isItem && (isSupper || editButton)" @show="showGetId">
        <span slot="reference">
          <slot>编辑</slot>
        </span>
      </el-popover>
    </div>
    <el-dropdown @command="handleCommand" @visible-change="showChange" trigger="hover" v-if="!isItem">
      <div class="setClassify__icon">
        <div style="margin-top: -4px">...</div>
      </div>
      <el-dropdown-menu slot="dropdown" class="commandClass">
        <el-dropdown-item v-for="item in dropdownList" :key="item.command" :disabled="item.disabled"
          :command="{ command: item.command, item: item }">{{ item.text }}</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from "vue-property-decorator";
import GoodCategory from "./GoodCategory.vue"; // 编辑商品分类
import GoodList from "../GoodsList.vue";
import {
  GoodCategroyType,
  GoodDetailInfo,
} from "@/views/goods/marketModel/goodType";
import ListApart from "../../ListApart.vue";
import { updateGoodShow, getAllRegionList, getManageAllRegionList } from "@/api/good/goods";
import { ApiSpecArea } from "../../marketType";

@Component({
  components: {
    GoodCategory,
  },
})
export default class SetClassify extends Vue {
  name = "SetClassify";

  @Prop()
  isItem!: boolean;

  @Prop()
  goodIds!: number[];

  @Prop()
  showGetList!: GoodDetailInfo[];

  idList: string[] = [];

  regionList: Array<Partial<ApiSpecArea>> = [];

  popVisible = false;

  dropdownList: Array<Partial<ApiSpecArea>> = [
  ];

  dropdownItemList = [
    {
      text: "商品码",
      command: "3",
      disabled: false,
    },
    {
      text: "删除",
      command: "4",
      disabled: false,
    },
  ];


  menuName = "权益包列表";

  buttonList = [];

  isSupper = 0;

  editButtonCode = "packagesList.edit";
  editButton = false;

  deleteButtonCode = "packagesList.delete";
  deleteButton = false;

  setClassificationButtonCode = "packagesList.setClassification";
  setClassificationButton = false;

  pickButtonCode = "packagesList.pick";
  pickButton = false;

  banButtonCode = "packagesList.ban";
  banButton = false;

  moveButtonCode = "packagesList.move";
  moveButton = false;

  deliveryButtonCode = "packagesList.delivery";
  deliveryButton = false;

  exportButtonCode = "packagesList.export";
  exportButton = false;


  mounted() {
    this.buttonAuth();
  }

  buttonAuth() {

    this.isSupper = this.$STORE.userStore.userInfo.isSupper
    let authMenuButtonVos = this.$STORE.userStore.userInfo.authMenuButtonVos.filter(i => i.menuName == this.menuName)

    let buttonList = [];

    authMenuButtonVos.forEach(element => {
      buttonList.push(element.buttonCode);
    });

    this.buttonList = buttonList

    var editButtonData = buttonList.find(e => e == this.editButtonCode);

    if (editButtonData != null && editButtonData != undefined) {
      this.editButton = true;
    }

    var deleteButtonData = buttonList.find(e => e == this.deleteButtonCode);

    if (deleteButtonData != null && deleteButtonData != undefined) {
      this.deleteButton = true;
    }

    var pickButtonData = buttonList.find(e => e == this.pickButtonCode);

    if (pickButtonData != null && pickButtonData != undefined) {
      this.pickButton = true;
    }

    var banButtonData = buttonList.find(e => e == this.banButtonCode);

    if (banButtonData != null && banButtonData != undefined) {
      this.banButton = true;
    }

    var setClassificationButtonData = buttonList.find(e => e == this.setClassificationButtonCode);

    if (setClassificationButtonData != null && setClassificationButtonData != undefined) {
      this.setClassificationButton = true;
    }

    var moveButtonData = buttonList.find(e => e == this.moveButtonCode);

    if (moveButtonData != null && moveButtonData != undefined) {
      this.moveButton = true;
    }

    var deliveryButtonData = buttonList.find(e => e == this.deliveryButtonCode);

    if (deliveryButtonData != null && deliveryButtonData != undefined) {
      this.deliveryButton = true;
    }

    var exportButtonData = buttonList.find(e => e == this.exportButtonCode);
    if (exportButtonData != null && exportButtonData != undefined) {
      this.exportButton = true;
    }

    console.log(this.moveButton);
  }


  initData() {
    getManageAllRegionList({}).then(res => {
      res.data.forEach(item => {
        item.text = `移至${item.modeName}专区`;
        item.show = true;
        item.disabled = false;
        item.command = item.id;
      });

      this.regionList = res.data;
      const parentHtml = this.$parent as ListApart;
      this.regionList.forEach((v, i) => {
        if (v.id === parentHtml.saleMode) {
          res.data.splice(i, 1);
        }
      });
      console.log(this.dropdownList);
      if (this.isSupper || this.moveButton) {
        this.dropdownList = this.regionList.concat(this.dropdownList);
      }
      console.log(this.dropdownList);
    });
  }

  /**
   * 获取选择商品id
   */
  showGetId() {
    const list = (this.$parent.$refs.goodsList as GoodList).tableCheckedItem;
    this.idList = [];
    list.forEach(item => {
      this.idList.push(String(item.id));
    });
  }

  /**
   * 点击按钮
   */
  handleClick(e: Event) {
    this.$emit("click", e);
  }

  /**
   * 点击的下拉项
   */
  handleCommand(val: any) {
    let type = false;
    if (val.item.modeName) {
      type = true;
    }
    this.$emit("command", val.command, type);
  }

  showChange(e: boolean) {
    if (e) {
      this.regionList = [];
      this.dropdownList = [];
      if (this.isSupper || this.pickButton) {
        this.dropdownList.push({
          text: "批量上架",
          command: "6",
          disabled: false,
        })
      }
      if (this.isSupper || this.banButton) {
        this.dropdownList.push({
          text: "批量下架",
          command: "7",
          disabled: false,
        })
      }
      if (this.isSupper || this.deleteButton) {
        this.dropdownList.push({
          text: "删除",
          command: "8",
          disabled: false,
        })
      }
      if (this.isSupper || this.deliveryButton) {
        this.dropdownList.push({
          text: "批量设置运费模板",
          command: "9",
          disabled: false,
        })
      }
      if (this.isSupper || this.exportButton) {
        this.dropdownList.push({
          text: "导出",
          command: "10",
          disabled: false,
        })
      }
      this.initData();
    }
  }

  /**
   * 商品单独操作
   */
  handleItemCommand(val: number) {
    this.$emit("handleItemCommand", val);
  }

  getNewCateList(List: GoodCategroyType[]) {
    if (this.idList.length === 0) {
      this.$message.error("请先选择商品");
      return;
    }
    const temList: GoodCategroyType[] = [];
    List.forEach(v => {
      if (v.showCategoryVos.length > 0) {
        temList.push(v);
      }
    });
    updateGoodShow(
      this.idList,
      JSON.parse(
        JSON.stringify(temList).replace(
          /showCategoryVos/g,
          "productShowCategorySeconds",
        ),
      ),
    ).then(res => {
      if (res.code === 200) {
        this.$message.success(`编辑成功`);
        this.popVisible = false;
        this.$emit("changeIds");
      }
    });
  }
}
</script>

<style lang="scss">
@import "@/assets/styles/mixins/mixins.scss";

@include b(setClassify) {
  display: flex;
  line-height: 30px;
  // border: 1px solid #dcdfe6;
  overflow: hidden;
  margin-left: 20px;
  border-radius: 50px;
  background-color: #eaf5fe;
  color: #309af3;
  border: 1px solid #eaf5fe;
  position: relative;

  @include e(title) {
    text-align: center;
    padding: 0 20px;
    cursor: pointer;
  }

  @include e(icon) {
    width: 40px;
    text-align: center;
    cursor: pointer;
    vertical-align: middle;
    color: #309af3;
    font-weight: bold;
  }
}

.setClassify__icon::after {
  color: #309af3;
  content: "|";
  position: absolute;
  left: -5px;
  bottom: 1px;
}

.commandClass {
  height: 150px;
  overflow: overlay;
}

.commandClass::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.commandClass::-webkit-scrollbar-thumb {
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: rgba(0, 0, 0, 0);
}

.commandClass::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 0;
  background: rgba(0, 0, 0, 0);
}
</style>
