<!--
 * @description: 抽离开源版本
 * @Author: chuyin<PERSON>
 * @Date: 2021-08-20 17:29:10
 * @LastEditors: latiao
 * @LastEditTime: 2022-01-14 10:14:53
-->
<template>
  <div>
    <!-- 顶部搜索条件 -->
    <Search @searchBy="getSearch" ref="Search" :changeId="changeId" :searchTypeProp="searchType"></Search>
    <div class="topLine">
      <div class="topLine__left">
        <el-button size="mini" type="primary" @click="publishGoods" v-if="isSupper || addButton">发布权益包</el-button>
        <!-- 设置分类 -->
        <div>
          <SetClassify @command="commandVal" ref="setClass" :goodIds="goodIds" :showGetList="showGetList"
            @changeIds="getGoodList" style="margin-left: 20px" :is-item="false" :is-value="false"></SetClassify>
        </div>

      </div>
    </div>
    <!-- 商品列表 -->
    <GoodsList @goodId="getGoodId" ref="goodsList" @getShowProList="getShowProList" :changeId="changeId"
      v-if="changeId"></GoodsList>
    <div class="listBottom">
      <!-- 设置分类 -->
      <div>
        <SetClassify @command="commandVal" ref="setClass" :goodIds="goodIds" :showGetList="showGetList"
          @changeIds="getGoodList" style="margin-left: 20px" :is-item="false" :is-value="false">设置分类</SetClassify>
      </div>
      <PageManage :pageSize="pageSize" :pageNum="pageNum" :total="total" @handleSizeChange="handleSizeChange"
        @handleCurrentChange="handleCurrentChange" style="margin-top: 0px"></PageManage>
    </div>

    <el-dialog title="批量设置运费模板" :visible.sync="showDelivery" width="30%">
      <div>
        <el-form :model="deliveryForm" :rules="rulesDelivery" ref="deliveryForm" label-width="100px">
          <el-form-item label="运费模板" prop="delivery">
            <el-select v-model="deliveryForm.delivery" placeholder="请选择运费模板" style="width: 60%;">
              <el-option v-for="item in optionsDelivery" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showDelivery = false">取 消</el-button>
        <el-button type="primary" @click="submitForm('deliveryForm')">确 定</el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop, Watch, Ref } from "vue-property-decorator";

import GoodsList from "./components/GoodsList.vue";
import SetClassify from "./components/goodsComp/SetClassify.vue";
import Search from "./components/Search.vue";
import PageManage from "@/components/PageManage.vue";
import { ListApartState } from "./marketType";
import { GoodDetailInfo } from "./goodType";
import { SearchKeyType } from "./components/searchType";

import { GoodDel, GoodUpDown, updateGoodsApart, getLogistics, updateProductFreightTemplate } from "@/api/good/goods";
import { exportPackageList } from "@/api/package/package";

@Component({
  components: {
    GoodsList,
    SetClassify,
    Search,
    PageManage
  }
})
export default class ListApart extends Vue implements ListApartState {
  @Prop({})
  saleMode!: string;

  changeId = "";

  @Watch("saleMode")
  getNewModeId() {
    this.changeId = this.saleMode;
  }

  @Ref()
  readonly setClass!: SetClassify;

  /** 获取商品数组信息 */
  @Ref()
  readonly goodsList!: GoodsList;

  goodIds: Array<string> = [];

  showGetList: Array<GoodDetailInfo> = [];

  searchType: SearchKeyType = {};

  pageSize = 0;

  pageNum = 0;

  total = 0;

  idList: Array<string | number> = [];

  menuName = "权益包列表";

  buttonList = [];

  isSupper = 0;

  addButtonCode = "packagesList.add";

  addButton = false;

  showDelivery = false;
  deliveryForm = {
    delivery: ''
  }
  optionsDelivery = []

  rulesDelivery = {
    delivery: [
      { required: true, message: '请选择运费模板', trigger: 'change' }
    ],

  }



  /** 配送设置内容 */
  rections = {
    autoShippingState: 1 /** 设置新增提货点是否自动设置送货上门,0false=禁用,1true=启用 */,
    goodPointIdsPercentage: ""
  };

  mounted() {
    // 加载搜索缓存
    var cache = JSON.parse(
      localStorage.getItem("cache_goods_search_form") || "{}"
    );
    this.searchType = Object.assign(cache) as SearchKeyType;

    this.changeId = this.saleMode;

    this.buttonAuth();
    this.getLogis();
  }

  buttonAuth() {
    this.isSupper = this.$STORE.userStore.userInfo.isSupper
    let authMenuButtonVos = this.$STORE.userStore.userInfo.authMenuButtonVos.filter(i => i.menuName == this.menuName)

    let buttonList = [];

    authMenuButtonVos.forEach(element => {
      buttonList.push(element.buttonCode);
    });

    this.buttonList = buttonList
    var addButtonData = buttonList.find(e => e == this.addButtonCode);
    console.log("addButtonData", addButtonData);
    if (addButtonData != null && addButtonData != undefined) {
      this.addButton = true;
    }

  }

  submitForm(formName) {
    this.$refs[formName].validate((valid) => {
      if (valid) {
        this.showDelivery = false;
        console.log(this.deliveryForm);
        console.log('this.goodIds=', this.goodIds);
        updateProductFreightTemplate(this.deliveryForm.delivery, this.goodIds).then(res => {
          this.$message({
            message: '批量修改运費模板成功',
            type: 'success'
          });
        }).catch(err => {
          this.$message.error(err)
        })
      } else {
        console.log('error submit!!');
        return false;
      }
    });
  }

  /**
   * 发布商品
   */
  publishGoods() {
    this.$router.push({
      name: "AddPackage",
      query: { saleMode: this.changeId }
    });
  }

  /** 根据选择批量操作 */
  commandVal(val: string, type = false) {
    const list = (this.$refs.goodsList as GoodsList).tableCheckedItem;
    list.forEach((item) => {
      this.goodIds.push(item.id as string);
    });
    if ("10" === val) {
      this.exportData();
      return;
    }
    if (this.goodIds.length === 0) {
      this.$message.error("请先选择商品");
      return;
    }
    if (type) {
      this.transApart(val);
      return;
    }
    switch (val) {
      case "8":
        this.delByIds();
        break;
      case "6":
      case "7":
        this.upDownGoods(val);
        break;
      case "9":
        this.upDelivery(val);
        break;
    }
  }
  /**
   * 批量转移专区
   */
  transApart(val: any) {
    const selectItem = this.goodsList.regionList.filter(
      (item) => item.id === val
    );
    const modeType = selectItem[0].modeType === "GROUP" ? "GROUP" : 0;
    updateGoodsApart(val, modeType, this.goodIds)
      .then((res) => {
        if (res.code === 200) {
          this.$message.success(`商品已${selectItem[0].text}`);
          this.getGoodList();
        }
      })
      .catch((e) => {
        this.$message.error(e || "网络错误");
      });
  }

  /**
   * 批量删除
   */
  async delByIds() {
    try {
      await this.$confirm("确定要删除选中商品吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      });
      const { code } = await GoodDel(this.goodIds, {});
      if (code === 200) {
        this.$message.success("删除成功");
        this.getGoodList();
      }
    } catch (error) {
      console.log(error);
    }
  }
 /**
   * 获取物流模板
   */
   async getLogis() {
    const res = await getLogistics({});
    res.data.unshift({
      id: "0",
      name: "商家包邮"
    });
    this.optionsDelivery = res.data;
  }
  /**
   * 批量上下架操作
   */
  async upDownGoods(val: string) {
    const list = this.goodsList.goodList;
    const hasNoType = list.some(
      (item) => item.productShowCategorys.length === 0
    );
    if (hasNoType) {
      this.$message.error("选择商品中有未编辑分类商品，请先编辑后在进行操作");
      return;
    }
    try {
      const productType = 2;
      const { code } = await GoodUpDown(val === "6" ? 1 : 0, 0, this.goodIds, productType);
      if (code === 200) {
        this.$message.success(`商品已${val === "6" ? "上架" : "下架"}`);
        this.getGoodList();
      }
    } catch (e) {
      this.$message.error(e);
    }
  }
  /**
     * 批量设置运费模板
     */
  upDelivery(val: string) {
    this.showDelivery = true;
  }
  /**
   * 获取选中商品ids数组
   */
  getGoodId(data: string[]) {
    this.goodIds = data;
  }

  /** 展示分类获取已选择的分类  */
  getShowProList(data: GoodDetailInfo[]) {
    this.showGetList = data || [];
    // this.goodsList = this.$refs.goodsList as GoodsList;
    this.total = this.goodsList.total;
    this.pageSize = this.goodsList.searchType.size as number;
    this.pageNum = this.goodsList.searchType.current as number;
  }

  /**
   * 获取商品列表
   */
  getGoodList() {
    this.goodsList.searchType = Object.assign(
      this.goodsList.searchType,
      this.searchType
    );
    this.goodsList.getProduct();
  }

  /**
   * 获取搜索条件
   */
  getSearch(data: SearchKeyType) {
    this.searchType = data;
    this.searchType.current = 1;

    // 缓存搜索条件
    localStorage.setItem(
      "cache_goods_search_form",
      JSON.stringify(this.searchType)
    );

    this.getGoodList();
  }

  /**
   * @method handleSizeChange
   * @description 每页 条
   */
  handleSizeChange(val: number) {
    this.goodsList.searchType.size = val;
    this.goodsList.getProduct();
  }

  /**
   * @method handleCurrentChange
   * @description 当前页
   */
  handleCurrentChange(val: number) {
    this.goodsList.searchType.current = val;
    this.goodsList.getProduct();
  }

  async exportData() {
    this.$confirm('最多导出10000条数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      const params = (this.$refs.goodsList as any).getProListParams();

      // 显示加载提示
      const loading = this.$loading({
        lock: true,
        text: '正在导出数据，请稍候...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });

      exportPackageList(params).then((res) => {
        loading.close();

        // 创建 Blob 对象
        const blob = new Blob([res.data], {
          type: "application/vnd.ms-excel;charset=UTF-8",
        });

        // 创建下载链接
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;

        // 生成文件名（带时间戳）
        const now = new Date();
        const timestamp = now.getFullYear() +
          String(now.getMonth() + 1).padStart(2, '0') +
          String(now.getDate()).padStart(2, '0') + '_' +
          String(now.getHours()).padStart(2, '0') +
          String(now.getMinutes()).padStart(2, '0') +
          String(now.getSeconds()).padStart(2, '0');

        link.setAttribute('download', `权益包列表_${timestamp}.xls`);
        document.body.appendChild(link);

        // 触发下载
        link.click();

        // 清理
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        this.$message.success('导出成功');

      }).catch((err) => {
        loading.close();
        console.error("导出失败:", err);
        this.$message.error('导出失败，请稍后重试');
      });
    }).catch(() => {
      // 用户取消导出
    });
  }
}
</script>

<style lang="scss" scoped>
@import "../../../assets/styles/goods/index.scss";

.topLine {
  display: flex;
  justify-content: space-between;
  align-items: center;

  &__left {
    display: flex;
  }

  &__right {
    width: 450px;
    display: flex;
    justify-content: space-around;
  }
}

.listBottom {
  margin-top: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: fixed;
  bottom: 10px;
  width: 990px !important;
  background-color: white;
  padding: 10px 0px;
  z-index: 10;
}
</style>
