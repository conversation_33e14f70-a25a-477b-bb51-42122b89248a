<template>
    <div class="box" >
        <div class="PackageCollect" v-if="this.$route.name === 'packageCollect'">
            <!-- 搜索 -->
            <m-card class="form" :needToggle="true">
                <el-form class="customer__dataForm" ref="dataFormRef" :model="dataForm" label-width="100px">
                    <el-row :gutter="40">
                        <el-col :span="10">
                            <el-form-item label="权益包名称"><el-input v-model="dataForm.packageName" clearable
                                    placeholder="请输入权益包名称" /></el-form-item>
                        </el-col>
                        <el-col :span="10">
                            <el-form-item label="日期">
                                <el-date-picker v-model="dataForm.date" type="daterange" value-format="yyyy-MM-dd"
                                    range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
                                </el-date-picker>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-button type="primary" style="margin-left:100px" @click="search()">搜索</el-button>
                </el-form>
            </m-card>

            <!-- 按钮 -->
            <div class="topLine">
              <el-button type="primary" style="margin-left:50px" @click="exportData()">导出列表</el-button>
            </div>
               <!-- 权益包汇总列表 -->
            <div class="table-list">
                <el-table show-summary :summary-method="getSummaries" :data="packageCollectList" border
                    style="width: 100%"
                    @sort-change="handleSortChange">
                    <el-table-column fixed prop="" label="权益包名称">
                        <template slot-scope="scope">
                            <div >
                                {{ scope.row.name }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column  sortable="custom" prop="packageQty" label="销售权益包数量">
                    </el-table-column>
                    <el-table-column  sortable="custom" prop="packageAmount" label="权益包金额"  >
                    </el-table-column>
                    <el-table-column prop="packageVerifyQty" label="核销数" >
                    </el-table-column>
                    <el-table-column prop="packageUnVerifyQty" label="未核销数" >
                    </el-table-column>
                </el-table>
            </div>

            <PageManage :pageSize="pageSize" :pageNum="pageNum" :total="total" @handleSizeChange="handleSizeChange"
                @handleCurrentChange="handleCurrentChange" />

        </div>

    </div>

</template>

<script>
import {managePackageStatic,managePackageStaticTotal, exportPackageStatic } from '@/api/package/package'
import PageManage from '@/components/PageManage'
export default {
    name: 'PackageCollect',//权益包汇总
    data() {
        return {
            packageCollectList: [

            ],
            tableData: [],
            /** 分页条数 */
            pageSize: 10,

            /** 分页页码 */
            pageNum: 1,

            /** 数据长度 */
            total: 0,
            // 数据表单  组成数据用于向后端提交数据
            dataForm: {
                storeName: "",
                startTime: "",
                endTime: "",
                packageName: "",
                date: ""
            },
        }
    },
    methods: {


        /**
         * @method 获取权益包汇总数据
         * @param srotArr handleSortChange方法传递过来的排序参数
         */
        getPackageCollectList(srotArr) {
             //srotArr为排序参数
             if (srotArr) {
                var { asc, orderBy } = srotArr
            }
            let form = this.dataForm
            //将日期格式化为字符串
            form.startTime = form.date[0]
            form.endTime = form.date[1]
            let params = {
                current: this.pageNum,
                size: this.pageSize,
                asc,
                orderBy,
                ...form
            }
            managePackageStatic(params).then(res => {
                this.packageCollectList = res.data.records;
                this.total = res.data.total;
                this.pageSize = res.data.size;
                this.pageNum = res.data.current;
            })
        },

        /**
         * @method 获取权益包汇总合计行数据
         */
        getPackageCollectTotal() {
            //因为是使用push方法，所以请求前要先置空数组
            this.tableData = []
            let form = this.dataForm
            let params = {
                current: this.pageNum,
                size: this.pageSize,
                ...form
            }
            managePackageStaticTotal(params).then(res => {
                let data = res.data
                this.tableData.push({ ...data })
            })
        },

        //搜索
        search() {
            this.getPackageCollectList(1);
            this.getPackageCollectTotal(1)
        },

        //合计行
        getSummaries(param) {
            // //将列表数据遍历到合计行
            let { columns } = param;
            let sums = [];
            columns.forEach((column, index) => {
                if (index === 0) {
                    sums[index] = '合计';
                } else {
                    const values = this.tableData.map(item => Number(item[column.property]));
                    if (!values.every(value => isNaN(value))) {
                        sums[index] = values.reduce((prev, curr) => {
                            const value = Number(curr);
                            if (!isNaN(value)) {
                                return prev + curr;
                            } else {
                                return prev;
                            }
                        }, 0);
                    }
                }
            });
            return sums;
        },

       /**
         * @method 排序函数
         * @param data 排序参数
         */
        handleSortChange(data) {
            let { order, prop } = data
            let params = {}
            if (order === 'ascending') {
                params.asc = 0
            } else if (order === 'descending') {
                params.asc = 1
            }
            //使用switch判断排序字段
            switch (prop) {
                case 'packageQty':
                    params.orderBy = 0;
                    break;
                case 'packageAmount':
                    params.orderBy = 1;
                    break;
                case 'productQty':
                    params.orderBy = 2;
                    break;
                case 'productAmount':
                    params.orderBy = 3;
                    break;
                case 'totalQty':
                    params.orderBy = 4;
                    break;
                case 'totalAmount':
                    params.orderBy = 5;
                    break;
                case 'memberQty':
                    params.orderBy = 6;
                    break;
            }
            this.getPackageCollectList(params)
        },

        /**
     * @method handleSizeChange
     * @description 每页 条
     */
        handleSizeChange(val) {
            this.pageSize = val;
            this.getPackageCollectList(1);
        },
        /**
         * @method handleCurrentChange
         * @description 当前页
         */
        handleCurrentChange(val) {
            this.pageNum = val;
            this.getPackageCollectList(val);
        },

        /**
         * 导出权益包汇总数据
         */
        exportData() {
            this.$confirm('最多导出10000条数据, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                let form = this.dataForm
                //将日期格式化为字符串
                form.startTime = form.date[0]
                form.endTime = form.date[1]
                let params = {
                    current: this.pageNum,
                    size: this.pageSize,
                    ...form
                }

                // 显示加载提示
                const loading = this.$loading({
                    lock: true,
                    text: '正在导出数据，请稍候...',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)'
                });

                exportPackageStatic(params).then((res) => {
                    loading.close();

                    // 创建 Blob 对象
                    const blob = new Blob([res.data], {
                        type: "application/vnd.ms-excel;charset=UTF-8",
                    });

                    // 创建下载链接
                    const url = window.URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = url;

                    // 生成文件名（带时间戳）
                    const now = new Date();
                    const timestamp = now.getFullYear() +
                        String(now.getMonth() + 1).padStart(2, '0') +
                        String(now.getDate()).padStart(2, '0') + '_' +
                        String(now.getHours()).padStart(2, '0') +
                        String(now.getMinutes()).padStart(2, '0') +
                        String(now.getSeconds()).padStart(2, '0');

                    link.setAttribute('download', `权益包汇总_${timestamp}.xls`);
                    document.body.appendChild(link);

                    // 触发下载
                    link.click();

                    // 清理
                    document.body.removeChild(link);
                    window.URL.revokeObjectURL(url);

                    this.$message.success('导出成功');

                }).catch((err) => {
                    loading.close();
                    console.error("导出失败:", err);
                    this.$message.error('导出失败，请稍后重试');
                });
            }).catch(() => {
                // 用户取消导出
            });
        }
    },
    mounted() {
        this.getPackageCollectList()
        this.getPackageCollectTotal()

    },
    components: {
        PageManage
    }
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/cutomer/customer';

.table-list {
    margin-top: 20px;
}
</style>
