<!--
 * @description: 抽离开源版本
 * @Author: chuyinlong
 * @Date: 2021-08-20 17:29:10
 * @LastEditors: chuyinlong
 * @LastEditTime: 2021-08-25 13:47:50
-->
<template>
  <!-- 设置分类 -->
  <div class="all">
    <!-- <div class="setClassify">
      <div @click="handleClick" class="setClassify__title" >发布优惠券</div>
    </div> -->

    <!-- <div class="el-dropdown-link">
      <el-dropdown @command="handleCommandsx">
        <span>
          {{ sort }}
        </span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item v-for="item in dropdownNum" :key="item.command"
            :command="{ command: item.command, item: item }">{{ item.text }}</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-dialog title="规则设置" :visible.sync="dialogVisible" width="60%" center :before-close="handleClose">
        <div style="margin-bottom: 5px;">通惠证说明</div>
        <RichEditor :text="descriptionName" ref="wEditor" style="min-height:100px;" onchange="changeValue"></RichEditor>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="dialogPrimary">确 定</el-button>
        </span>
      </el-dialog>
      <div class="dropdownsx">
        <i @click="commandSort(1)" :class="sortIndex == 1 ? 'el-icon1' : ''" class="el-icon-arrow-up el-icon--right"></i>
        <i @click="commandSort(2)" :class="sortIndex == 2 ? 'el-icon2' : ''" class="el-icon-arrow-down el-icon--right"></i>
      </div>
    </div> -->

  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop, Ref } from "vue-property-decorator";
import { ApiSpecArea } from "../../marketType";
import RichEditor from "@/components/RichEditor.vue";
import { passTicketRule, saveRule } from "@/api/certificateApi/certificateApi"

@Component({
  components: {
    RichEditor
  },
})
export default class SetClassify extends Vue {
  name = "SetClassify";

  @Ref()
  readonly wEditor: HTMLFormElement;

  @Prop()
  isItem!: boolean;

  @Prop()
  goodIds!: number[];

  idList: string[] = [];
  popVisible = false;
  sort = '排序'
  sortIndex = ''
  descriptionName = ''
  dialogVisible = false


  dropdownNum = [
    {
      text: "生效时间",
      command: "startTimeSort",
      disabled: false,
    },
    {
      text: "过期时间",
      command: "endTimeSort",
      disabled: false,
    }, {
      text: "售价",
      command: "priceSort",
      disabled: false,
    }, {
      text: "使用次数",
      command: "useableTimesSort",
      disabled: false,
    }
  ];
  dropdownList: Array<Partial<ApiSpecArea>> = [
        {
          text: "规则设置",
          command: "rule",
          disabled: false,
        },
      ];
  
  menuName = "通惠证";

  buttonList = [];

  isSupper = 0;

  addButtonCode = "certificateList.add";

  addButton = false;

  ruleSettingButtonCode = "certificateList.ruleSetting";

  ruleSettingButton = false;

  exportButtonCode = "packageDetailList.export";
  exportButton = false;

  mounted() {
		this.buttonAuth();
	}

  buttonAuth() {

    this.isSupper = this.$STORE.userStore.userInfo.isSupper
		let authMenuButtonVos = this.$STORE.userStore.userInfo.authMenuButtonVos.filter(i => i.menuName == this.menuName)

		let buttonList = [];

		authMenuButtonVos.forEach(element => {
			buttonList.push(element.buttonCode);
		});

		this.buttonList = buttonList

    var addButtonData = buttonList.find(e => e == this.addButtonCode);

    if (addButtonData != null && addButtonData != undefined) {
      this.addButton = true;
    }

    var ruleSettingButtonData = buttonList.find(e => e == this.ruleSettingButtonCode);

    if (ruleSettingButtonData!= null && ruleSettingButtonData != undefined) {
      this.ruleSettingButton = true;
    }

    var exportButtonData = buttonList.find(e => e == this.exportButtonCode);
    if (exportButtonData != null && exportButtonData != undefined) {
      this.exportButton = true;
    }

    this.dropdownList = [];
    if(this.ruleSettingButton||this.isSupper){
      this.dropdownList.push({
        text: "规则设置",
        command: "rule",
        disabled: false,
      });
    }
    if (this.isSupper || this.exportButton) {
      this.dropdownList.push({
        text: "导出",
        command: "export",
        disabled: false,
      });
    }

  }
  changeValue(val: any) {
    console.log("sss", val);
  }
  /**
   * 发布积分方案
   */
  handleClick() {
    this.$router.push({
      name: "addCoupon"
    });
  }
  getTicketRule() {
    passTicketRule({}).then((res) => {
      console.log('res', res.data);
      if (res.data) {
        this.descriptionName = res.data.rule
      }

    }).catch((err) => {
      this.$message.error(err)
    })
  }
  handleClose(done) {
    this.$confirm('确认关闭？')
      .then(_ => {
        done();
      })
      .catch(_ => { });
  }
  dialogPrimary(){
    let textName=this.wEditor.getHtml()
    saveRule({rule:textName}).then((res) => {
      this.$message.success('保存成功')
      this.dialogVisible=false;
    }).catch((err) => {
      this.$message.error(err)
    })
    
  }

  /**
 * 点击的下拉项
 */
  handleCommand(val: any) {
    let type = false;
    if (val.item.modeName) {
      type = true;
    }
    this.dialogVisible = true
    this.getTicketRule()
    this.$emit("command", val.command, type);
  }
  showChange(e: boolean) {
    if (e) {
      this.regionList = [];
      this.dropdownList = [];
      if(this.ruleSettingButton||this.isSupper){
        this.dropdownList.push({
          text: "规则设置",
          command: "rule",
          disabled: false,
        });
      }
      if (this.isSupper || this.exportButton) {
        this.dropdownList.push({
          text: "导出",
          command: "export",
          disabled: false,
        });
      }
      // this.initData();
    }
  }
  handleCommandsx(val: any) {
    this.sort = val.item.text
    this.sortcommand = val.item.command
    this.sortIndex = ''
  }
  commandSort(val: any) {
    if (this.sortcommand) {
      this.sortIndex = val
      this.$emit("commandsx", val, this.sortcommand);
    } else {
      this.$message.error("请先选择排序");
    }
  }
}
</script>

<style lang="scss">
@import "@/assets/styles/mixins/mixins.scss";

@include b(setClassify) {
  display: flex;
  line-height: 30px;
  // border: 1px solid #dcdfe6;
  overflow: hidden;
  margin-left: 20px;
  border-radius: 50px;
  background-color: #409EFF;
  color: #ffffff;
  border: 1px solid #409EFF;
  position: relative;

  @include e(title) {
    text-align: center;
    padding: 0 20px;
    cursor: pointer;
  }

  @include e(icon) {
    width: 40px;
    text-align: center;
    cursor: pointer;
    vertical-align: middle;
    color: #ffffff;
    font-weight: bold;
  }
}

.all {
  display: flex;
  justify-content: space-between;

  .el-dropdown-link {
    height: 32px;
    width: 130px;
    line-height: 20px;
    border-radius: 20px;
    background-color: rgba(255, 255, 255, 1);
    color: rgba(16, 16, 16, 1);
    font-size: 14px;
    text-align: left;
    border: 1px solid rgba(187, 187, 187, 1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px;
    margin-left: 17px;

    .el-dropdown {
      width: 80px;

      span {
        cursor: pointer;

      }
    }

    .dropdownsx {
      display: flex;
      flex-direction: column;

      .el-icon1 {
        color: #409EFF;
      }

      .el-icon2 {
        color: #409EFF;
      }
    }
  }

}

.setClassify__icon::after {
  color: #ffffff;
  content: "|";
  position: absolute;
  left: -5px;
  bottom: 1px;
}

.commandClass {
  height: 100px;
  overflow: overlay;
}

.commandClass::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.commandClass::-webkit-scrollbar-thumb {
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: rgba(0, 0, 0, 0);
}

.commandClass::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 0;
  background: rgba(0, 0, 0, 0);
}
</style>
