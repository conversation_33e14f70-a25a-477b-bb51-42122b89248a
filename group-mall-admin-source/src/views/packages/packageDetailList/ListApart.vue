<template>
	<div>
		<!-- 顶部搜索条件 -->
		<Search @searchBy="getSearch" ref="Search" :status="chooseStatus" :searchTypeProp="searchType"></Search>
		<!-- <el-tabs v-model="activeName" @tab-click="handleClick">
			<el-tab-pane v-for="(item, index) in list" :key="index" :label="item.modeName" :name="item.modeName"
				:status="item.status"></el-tab-pane>
		</el-tabs> -->
		<div class="topLine">
<!--			<div class="topLine__left">
				&lt;!&ndash; 创建优惠券 &ndash;&gt;
				<SetClassify @commandsx="commandValsx" ref="setClass" :goodIds="chooseStatus" style="margin-left: 20px"
					:is-item="false" :is-value="false"/>
			</div>-->
        <div class="topLine__left">
          <div class="all">
            <div class="setClassify">
              <div @click="exportData" class="setClassify__title" >导出列表</div>
            </div>
          </div>
        </div>
		</div>
		<!-- 商品列表 -->
		<PagingList @goodId="getGoodId" ref="pagingList" @getShowProList="getShowProList" :changeId="chooseStatus">
		</PagingList>
		<!-- 设置分类 -->
		<div class="listBottom">
			<PageManage :pageSize="pageSize" :pageNum="pageNum" :total="total" @handleSizeChange="handleSizeChange"
				@handleCurrentChange="handleCurrentChange" style="margin-top: 0px"></PageManage>
		</div>
	</div>
</template>

<script lang="ts">
import { Vue, Component, Prop, Watch, Ref } from "vue-property-decorator";
import Search from "./components/Search.vue";
import PagingList from "./components/pagingList.vue";
import SetClassify from "./components/goodsComp/SetClassify.vue";
import { SearchKeyType } from "./components/searchType";
import PageManage from "@/components/PageManage.vue";
import { GoodDetailInfo } from "./goodType";
import { exportPackageDetail } from "@/api/package/package";
@Component({
	components: {
		Search,
		SetClassify,
		PagingList,
		PageManage
	}
})
export default class ListApart extends Vue {
	// @Prop({})
	// chooseStatus!: string;
	// @Watch("chooseStatus")
	// getNewModeId() {
	//     this.status = this.chooseStatus;
	//     console.log('获取商品数组信息',this.changeId,this.chooseStatus);

	// }
	/** 获取商品数组信息 */
	@Ref()
	readonly pagingList!: PagingList;

	status = "";
	searchType: SearchKeyType = {};
	pageSize = 0;
	pageNum = 0;
	total = 0;
	// 状态:100->未生效;101->已生效;104->已过期;200->驳回;300->终止
	list: any = [{ modeName: '已生效', status: 101 },
	{ modeName: '未生效', status: 100 },
	{ modeName: '已过期', status: 104 },
	{ modeName: '已停用', status: 300 },
	{ modeName: '全部', status: '' }
	]
	activeName = '已生效';
	chooseStatus: number = 101;
	/**
	 * 顶部专区选择
	 */
	handleClick(tab: { index: number }) {
		this.chooseStatus = this.list[tab.index].status || '';
	}
	/**
	 * 获取搜索条件
	 */
	getSearch(data: SearchKeyType) {
		this.searchType = data;
		this.searchType.current = 1;
		console.log('获取搜索条件', data);

		// 缓存搜索条件
		this.getPagingList();
	}
	/** 展示分类获取已选择的分类  */
	getShowProList(data: GoodDetailInfo[]) {
		this.showGetList = data || [];
		// this.pagingList = this.$refs.pagingList as GoodsList;
		this.total = this.pagingList.total;
		// console.log('fffff',this.total);
		this.pageSize = this.pagingList.searchType.size as number;
		this.pageNum = this.pagingList.searchType.current as number;
	}
	/** 根据选择批量操作 */
	commandValsx(val: string, sortcommand: string) {
		if (val === "export") {
			this.exportData();
			return;
		}
		let types: any = {}
		types[sortcommand] = val;
		this.searchType = types;
		for (let key in this.pagingList.searchType) {
			if (key == 'endTimeSort' || key == 'priceSort' || key == 'startTimeSort' || key == 'useableTimesSort') {
				this.$delete(this.pagingList.searchType, key)
			}
		}
		console.log('0000', this.pagingList.searchType);
		this.getPagingList();

	}
	/**
	 * 获取商品列表
	 */
	getPagingList() {
		this.pagingList.searchType = Object.assign(
			this.pagingList.searchType,
			this.searchType
		);
		this.pagingList.getPageTicket();
	}
	/**
	 * @method handleSizeChange
	 * @description 每页 条
	 */
	handleSizeChange(val: number) {
		this.pagingList.searchType.size = val;
		this.pagingList.getPageTicket();
	}

	/**
	 * @method handleCurrentChange
	 * @description 当前页
	 */
	handleCurrentChange(val: number) {
		this.pagingList.searchType.current = val;
		console.log('当前页', val);

		this.pagingList.getPageTicket();
	}
	/**
	 * 获取选中商品ids数组
	 */
	getGoodId(data: string[]) {
		// this.goodIds = data;
	}

	async exportData() {
		this.$confirm('最多导出10000条数据, 是否继续?', '提示', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning'
		}).then(() => {
			const params = this.pagingList.searchType;

			// 显示加载提示
			const loading = this.$loading({
				lock: true,
				text: '正在导出数据，请稍候...',
				spinner: 'el-icon-loading',
				background: 'rgba(0, 0, 0, 0.7)'
			});

			exportPackageDetail(params).then((res) => {
				loading.close();

				// 创建 Blob 对象
				const blob = new Blob([res.data], {
					type: "application/vnd.ms-excel;charset=UTF-8",
				});

				// 创建下载链接
				const url = window.URL.createObjectURL(blob);
				const link = document.createElement('a');
				link.href = url;

				// 生成文件名（带时间戳）
				const now = new Date();
				const timestamp = now.getFullYear() +
					String(now.getMonth() + 1).padStart(2, '0') +
					String(now.getDate()).padStart(2, '0') + '_' +
					String(now.getHours()).padStart(2, '0') +
					String(now.getMinutes()).padStart(2, '0') +
					String(now.getSeconds()).padStart(2, '0');

				link.setAttribute('download', `权益包明细_${timestamp}.xls`);
				document.body.appendChild(link);

				// 触发下载
				link.click();

				// 清理
				document.body.removeChild(link);
				window.URL.revokeObjectURL(url);

				this.$message.success('导出成功');

			}).catch((err) => {
				loading.close();
				console.error("导出失败:", err);
				this.$message.error('导出失败，请稍后重试');
			});
		}).catch(() => {
			// 用户取消导出
		});
	}
}

</script>

<style></style>
