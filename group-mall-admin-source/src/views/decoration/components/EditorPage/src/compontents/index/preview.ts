/*
 * @description: 抽离开源版本
 * @Author: chuy<PERSON><PERSON>
 * @Date: 2021-08-20 17:29:10
 * @LastEditors: chuyinlong
 * @LastEditTime: 2021-08-23 16:02:18
 */
import HomeSwiper from "../HomeSwiper/Preview.vue";
import GoodSwiper from "../GoodSwiper/Preview.vue";
import Search from "../Search/Preview.vue";
import AddressSet from "../AddressSet/Preview.vue";
import Goods from "../Goods/Preview.vue";
import TitleBar from "../TitleBar/Preview.vue";
import BlankPaceholder from "../BlankPaceholder/Preview.vue";
import Separator from "../Separator/Preview.vue";
import StoreNavigation from "../StoreNavigation/Preview.vue";
import NavBar from "../NavBar/Preview.vue";
import CubeBox from "../CubeBox/Preview.vue";
import RichText from "../RichText/Preview.vue";
import BusinessSuper from "../../../../BusinessSuper/Preview.vue";
import ImageCom from "../ImageCom/Preview.vue";
import VideoCom from "../VideoCom/Preview.vue";
import StoreInformationCom from "../StoreInformationCom/Preview.vue";
export default {
  HomeSwiper,
  Search,
  AddressSet,
  Goods,
  TitleBar,
  BlankPaceholder,
  Separator,
  StoreNavigation,
  NavBar,
  CubeBox,
  RichText,
  BusinessSuper,
  ImageCom,
  VideoCom,
  GoodSwiper,
  StoreInformationCom,
};
