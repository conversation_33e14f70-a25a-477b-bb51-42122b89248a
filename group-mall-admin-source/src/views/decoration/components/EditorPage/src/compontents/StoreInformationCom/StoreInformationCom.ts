/*
 * @description: 抽离开源版本
 * @Author: chuyinlong
 * @Date: 2021-08-20 17:29:10
 * @LastEditors: chuyinlong
 * @LastEditTime: 2021-08-25 11:06:11
 */
interface IShowContent {
  /** 商品名称 */
  nameShow: boolean;
  /** 商品价格 */
  priceShow: boolean;
  /** 购物按钮 */
  buttonShow: boolean;
  /** 购物按钮样式 */
  buttonStyle: number;
  /** 购物车按钮文案 */
  buttonText: string;
  /** 商品角标 */
  tagShow: boolean;
  /** 商品角标样式 */
  tagStyle: number;
}

interface GoodsItem {
  id: number;
  /** 商品名称 */
  name: string;
  /** 商品描述 */
  saleDescribe: string;
  /** 商品图片 */
  img: string;
  /** 结束时间 */
  endTime: string;
  /** 商品实售价 */
  price: number;
  /** 商品指导价 */
  guide: number;
  /** 已售量 */
  soldCount: number;
  /** 库存 */
  inventory: number;
  /** 提货时间 */
  deliveryTime: string;
}

/**
 * 商品
 */
export default class Goods {

  /** 页面边距 */
  pagePadding = 17;
  checked1=false//左上角
  checked2=false//右上角
  checked3=false//右下角
  checked4=false//左下角
  width=345
  height=150;
  roundedPixels = 17;

  /** 商品间距 */
  Padding = 10;
}

export interface ICategoryItem {
  id: string | number;
  name: string;
  saleDescribe: string;
  productNumber: number | string;
  saleMode: string;
  shopName:string;
  showCategoryVos: ICategoryItemVos[];
}

export type ICategoryItemVos = Record<"id" | "name" | "productNumber", string>;

export interface ICategoryMode {
  id: string;
  modeName: string;
  modeType: string;
  productNumber: string | number;
  sort: number;
}

export interface ISubFormGoods {
  albumPics: string;
  id: string;
  img?: string;
  pic?: string;
  name: string;
  saleDescribe: string;
  price?: string | number;
  saleMode: string;
  minPrice?: string;
  maxPrice?: string;
  shopName:string;
  sale?: string | number;
  shopsCategoryName:string;
  categoryName:string;
}
