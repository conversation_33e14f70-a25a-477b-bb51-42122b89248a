
export interface SearchState {
    searchType: SearchKeyType;//条件搜索
    value1: string;//单据日期时间范围
    value2: string;//有效期时间范围
}

export interface SearchKeyType {
    billDateStartTime?: string;//单据日期开始时间
    billDateEndTime?: string;//单据日期结束时间
    userName?: string;//经手人
    createUserName?: string;//制单人
    name?: string;//方案名称
    startTime?: string;//有效期开始时间
    endTime?: string;//有效期结束时间
    rewardType?: string;//奖励类型
    productName?: string;//商品名称
    memberLevelName?: string;//会员等级名称
    current?: number;//当前页
    size?:number;//每页条数
}