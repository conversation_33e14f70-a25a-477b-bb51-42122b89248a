/*
 * @description: 抽离开源版本
 * @Author: chuy<PERSON><PERSON>
 * @Date: 2021-08-20 17:29:06
 * @LastEditors: chuyinlong
 * @LastEditTime: 2021-08-26 09:53:37
 */
import api from "@/libs/request";


/** 分页查询用户权益包订单列表*/
export const pagePackageOrder = (data : any) => {
	return api.post("/account-open/mini-account-package-order/getPageList", data);
};

/** 分页获取权益包明细*/
export const getProductPackageDetailVo = (data : any) => {
	return api.post("/goods-open/manager/product/getProductPackageDetailVo", data);
};


/** 分页查询用户权益包订单商品列表*/
export const pagePackageGoods = (data : any) => {
	return api.post("/account-open/mini-account-package-goods/getPageList", data);
};
/** 分页获取商家用户查询自己核销的记录*/
export const pageUserPackageGoods = (data : any) => {
	return api.post("/account-open/mini-platform/packageGoods/pageUserPackageGoods", data);
};
/** 分页获取核销单明细*/
export const pagePackageGoodsCodeDetail = (data : any) => {
	return api.post("/account-open/mini-platform/packageGoods/pagePackageGoodsCodeDetail", data);
};

/** 订单汇总*/
export const packageCollect = (data : any) => {
	return api.get("/order-open/manage/manageOrderStatic",data)
}

/**
 * 获取订单汇总总计数据
 */
export const packageCollectTotal = (data : any) => {
	return api.get("/order-open/manage/manageOrderStaticTotal",data)
}

/**
 * 获取权益包汇总列表数据
 */
export const managePackageStatic = (data : any) => {
	return api.get("/order-open/manage/managePackageStatic",data)
}

/**
 * 获取权益包汇总总计数据
 */
export const managePackageStaticTotal = (data : any) => {
	return api.get("/order-open/manage/managePackageStaticTotal",data)
}

/**
 * 导出权益包列表
 */
export const exportPackageList = (data: any) => {
	return api.get("/goods-open/manager/product/export/package", data);
};

/**
 * 导出权益包明细
 */
export const exportPackageDetail = (data: any) => {
	return api.post("/goods-open/manager/product/exportProductPackageDetailVo", data);
};

/**
 * 导出销售记录
 */
export const exportPackageOrder = (data: any) => {
	return api.post("/account-open/mini-account-package-order/export", data);
};

/**
 * 导出销售明细
 */
export const exportPackageGoods = (data: any) => {
	return api.post("/account-open/mini-account-package-goods/export", data);
};

/**
 * 导出权益包汇总
 */
export const exportPackageStatic = (data: any) => {
	return api.get("/order-open/manage/export/packageStatic", data);
};